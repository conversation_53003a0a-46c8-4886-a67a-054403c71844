import { ExecutionLog, ExtractPdfValuesStep, getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import { LLMService } from '../../../services/llm/LLMService';
import { LLMProviderFactory } from '../../../services/llm/LLMProviderFactory';

/**
 * LLM processing step executors for AIRunner
 */

export interface LlmProcessingExecutorContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
  // openai: OpenAI; // TA BORT denna rad
}

// Local type definition until shared package is updated
interface ProcessWithLLMStep {
  id: string;
  type: 'processWithLLM';
  name: string;
  description?: string;
  timeout?: number;
  textInput: string; // Text to process or variable reference
  prompt: string; // User prompt for LLM processing
  variableName?: string; // Variable name to store AI response (default: 'var-ai-response')
  model?: string; // LLM model to use (uses LLM_DEFAULT_MODEL if not specified)
  temperature?: number; // Temperature for AI response (default: 0.3)
  maxTokens?: number; // Max tokens for response (default: 2000)
}

/**
 * Execute processWithLLM step - processes text with OpenAI LLM
 */
export async function executeProcessWithLLM(
  step: ProcessWithLLMStep,
  context: LlmProcessingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables } = context;

  try {
    // Interpolate inputs
    const interpolatedTextInput = interpolateVariables(step.textInput, variables);
    const interpolatedPrompt = interpolateVariables(step.prompt, variables);

    // Get provider and model info for logging
    const provider = LLMProviderFactory.getInstance();
    const model = step.model || LLMProviderFactory.getDefaultModel();

    onLog({
      level: 'info',
      message: `Sending text to ${provider.name} (${model}) for processing...`,
      stepId: step.id
    });

    // Send to LLM for processing
    const completion = await LLMService.createChatCompletion([
      {
        role: 'system',
        content: 'Du är en AI-assistent som hjälper till att extrahera och bearbeta information från dokument. Svara på svenska och var koncis och tydlig.'
      },
      {
        role: 'user',
        content: `Här är texten som ska bearbetas:\n\n${interpolatedTextInput}\n\nAnvändarens instruktion: ${interpolatedPrompt}\n\nBearbeta texten enligt instruktionen och ge ett tydligt svar.`
      }
    ], {
      model: step.model, // Använd LLMProviderFactory.getDefaultModel() om step.model är undefined
      temperature: step.temperature || 0.3,
      maxTokens: step.maxTokens || 2000
    });

    const aiResponse = completion.content;
    if (!aiResponse) {
      throw new Error('No response from LLM');
    }

    // Store result in variable
    const variableName = step.variableName || 'var-ai-response';
    variables[variableName] = aiResponse;

    onLog({
      level: 'info',
      message: `LLM processing completed. Result stored in variable: ${variableName}`,
      stepId: step.id,
      data: { [variableName]: aiResponse.substring(0, 200) + (aiResponse.length > 200 ? '...' : '') }
    });

    return {
      success: true,
      variables: { [variableName]: aiResponse }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Failed to process text with LLM: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * PDF extraction and LLM processing with JSON value extraction
 */
export async function executeExtractPdfValues(
  step: ExtractPdfValuesStep & { variableName: string },
  context: LlmProcessingExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog, interpolateVariables } = context;

  try {

    // Interpolate base64Input to handle variable references
    const interpolatedBase64Input = interpolateVariables(step.base64Input, variables);
    const interpolatedPrompt = interpolateVariables(step.prompt, variables);

    onLog({
      level: 'info',
      message: 'Starting PDF text extraction...',
      stepId: step.id,
      data: {
        originalBase64Input: step.base64Input,
        interpolatedLength: interpolatedBase64Input.length,
        availableVariables: Object.keys(variables),
        interpolatedPreview: interpolatedBase64Input.substring(0, 100) + (interpolatedBase64Input.length > 100 ? '...' : '')
      }
    });

    // Extract base64 data (remove data URL prefix if present)
    let base64Data = interpolatedBase64Input;
    if (base64Data.includes(',')) {
      base64Data = base64Data.split(',')[1];
    }

    // Validate base64 data
    if (!base64Data || base64Data.trim() === '') {
      throw new Error('Base64 input is empty or invalid');
    }

    // Convert base64 to buffer with error handling
    let pdfBuffer: Buffer;
    try {
      pdfBuffer = Buffer.from(base64Data, 'base64');
    } catch (bufferError) {
      throw new Error(`Invalid base64 data: ${bufferError instanceof Error ? bufferError.message : 'Unknown error'}`);
    }

    onLog({
      level: 'info',
      message: `PDF buffer created, size: ${pdfBuffer.length} bytes`,
      stepId: step.id
    });

    // Basic PDF validation - check for PDF header
    const pdfHeader = pdfBuffer.slice(0, 4).toString();
    if (pdfHeader !== '%PDF') {
      onLog({
        level: 'error',
        message: `Invalid PDF header: expected '%PDF', got '${pdfHeader}'. Buffer starts with: ${pdfBuffer.slice(0, 20).toString('hex')}`,
        stepId: step.id
      });
      throw new Error(`Invalid PDF structure: File does not start with PDF header. Got: ${pdfHeader}`);
    }

    // Extract text from PDF using pdfjs-dist
    let extractedText: string;
    try {
      // Import pdfjs-dist for CommonJS environment
      const pdfjs = require('pdfjs-dist/legacy/build/pdf.mjs');

      // Configure for Node.js environment
      const pdfjsLib = pdfjs;

      onLog({
        level: 'info',
        message: 'Loading PDF document with pdfjs-dist...',
        stepId: step.id
      });

      const loadingTask = pdfjsLib.getDocument({
        data: new Uint8Array(pdfBuffer),
        useSystemFonts: true,
        disableFontFace: true,
        verbosity: 0 // Reduce console noise
      });

      const pdf = await loadingTask.promise;

      onLog({
        level: 'info',
        message: `PDF loaded successfully. Pages: ${pdf.numPages}`,
        stepId: step.id
      });

      let fullText = '';
      for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .filter((item: any) => 'str' in item)
          .map((item: any) => item.str)
          .join(' ');
        fullText += pageText + '\n';
      }

      extractedText = fullText.trim();

      onLog({
        level: 'info',
        message: 'PDF text extracted successfully using pdfjs-dist',
        stepId: step.id
      });
    } catch (pdfjsError) {
      const errorMessage = pdfjsError instanceof Error ? pdfjsError.message : 'Unknown error';
      onLog({
        level: 'error',
        message: `PDF parsing failed with pdfjs-dist: ${errorMessage}`,
        stepId: step.id,
        data: {
          bufferSize: pdfBuffer.length,
          bufferStart: pdfBuffer.slice(0, 20).toString('hex'),
          errorType: pdfjsError instanceof Error ? pdfjsError.constructor.name : typeof pdfjsError
        }
      });
      throw new Error(`PDF parsing failed: ${errorMessage}`);
    }

    onLog({
      level: 'info',
      message: `Extracted ${extractedText.length} characters from PDF`,
      stepId: step.id
    });

    // Get provider and model info for logging
    const provider = LLMProviderFactory.getInstance();
    const defaultModel = LLMProviderFactory.getDefaultModel();

    onLog({
      level: 'info',
      message: `Sending extracted text to ${provider.name} (${defaultModel}) for processing...`,
      stepId: step.id
    });

    // Create JSON system prompt for structured data extraction
    const jsonSystemPrompt = `Du är en AI-assistent som hjälper till att extrahera specifika värden från PDF-dokument.

VIKTIGT: Du MÅSTE alltid svara med ett giltigt JSON-objekt. Inget annat format accepteras. Du skall svara med ett JSON-objekt som innehåller alla värden som begärs i användarens instruktion.
Objektet skall vara "platt", dvs. inte innehålla andra objekt eller arrayer.
Bara belopp skall retuneras, så om det tex står "kr" bakom beloppet så skall inte "kr" tas med i svaret.

Exempel på korrekt svar:
{
  "namn": "John Doe",
  "telefon": "08-123 456 78",
  "email": "<EMAIL>",
  "datum": "2024-01-15"
}

Om du inte kan hitta ett värde, använd null som värde. Svara alltid på svenska.`;

    // Send to LLM for processing
    const completion = await LLMService.createChatCompletion([
      {
        role: 'system',
        content: jsonSystemPrompt
      },
      {
        role: 'user',
        content: `Här är texten från ett PDF-dokument:\n\n${extractedText}\n\nAnvändarens instruktion: ${interpolatedPrompt}\n\nExtrahera de begärda värdena och svara med ett JSON-objekt.`
      }
    ], {
      temperature: 0.1, // Lower temperature for more consistent JSON output
      maxTokens: 2000
    });

    const aiResponse = completion.content;
    if (!aiResponse) {
      throw new Error('No response from LLM');
    }

    onLog({
      level: 'info',
      message: `${provider.name} response received, parsing JSON...`,
      stepId: step.id
    });

    // Parse JSON response and create variables for each value
    let parsedJson: Record<string, any>;
    try {
      // Clean the response to extract JSON
      let jsonStr = aiResponse.trim();

      // Remove markdown code blocks if present
      if (jsonStr.startsWith('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonStr.startsWith('```')) {
        jsonStr = jsonStr.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      parsedJson = JSON.parse(jsonStr);

      if (typeof parsedJson !== 'object' || parsedJson === null || Array.isArray(parsedJson)) {
        throw new Error('Response is not a valid JSON object');
      }

    } catch (parseError) {
      const errorMessage = parseError instanceof Error ? parseError.message : 'Unknown JSON parsing error';
      onLog({
        level: 'error',
        message: `Failed to parse ${provider.name} response as JSON: ${errorMessage}`,
        stepId: step.id,
        data: { rawResponse: aiResponse.substring(0, 500) }
      });

      throw new Error(`Invalid JSON response from ${provider.name}: ${errorMessage}`);
    }

    // Store the entire JSON object in the specified variable
    const variableName = step.variableName || getDefaultVariableName('extractPdfValues', stepIndex);
    variables[variableName] = parsedJson;

    onLog({
      level: 'info',
      message: `PDF values extraction completed. Stored result in variable: ${variableName}`,
      stepId: step.id,
      data: { [variableName]: parsedJson }
    });

    return {
      success: true,
      variables: { [variableName]: parsedJson }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Failed to extract PDF text: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  }
}
