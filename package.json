{"name": "rpa-project", "version": "1.0.0", "description": "Complete RPA project with TypeScript, React Flow, and Playwright", "private": true, "workspaces": ["frontend", "backend", "shared"], "scripts": {"build": "npm run build:shared && npm run build:backend && npm run build:frontend", "build:shared": "cd shared && npm run build", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "dev": "concurrently \"npm run dev:shared\" \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:shared": "cd shared && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "clean": "npm run clean:shared && npm run clean:backend && npm run clean:frontend", "clean:shared": "cd shared && npm run clean", "clean:backend": "cd backend && npm run clean", "clean:frontend": "cd frontend && npm run clean", "type-check": "tsc --build", "type-check:watch": "tsc --build --watch", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../shared && npm install", "setup": "npm run install:all && npm run build", "generate:step": "node tools/generate-step.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f"}, "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^6.0.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"bullmq": "^5.56.4", "cron-parser": "^5.3.0", "pdfjs-dist": "^5.3.93"}}